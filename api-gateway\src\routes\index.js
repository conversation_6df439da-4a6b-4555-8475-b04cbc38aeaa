const express = require('express');
const { verifyToken, verifyInternalService, verifyAdmin } = require('../middleware/auth');
const { authLimiter, assessmentLimiter, adminLimiter } = require('../middleware/rateLimiter');
const { authServiceProxy, archiveServiceProxy, assessmentServiceProxy } = require('../middleware/proxy');

const router = express.Router();

/**
 * API Gateway Routes Configuration
 * 
 * Route Structure:
 * /api/auth/* -> Auth Service
 * /api/admin/* -> Auth Service (Admin endpoints)
 * /api/archive/* -> Archive Service
 * /api/assessment/* -> Assessment Service
 */

// ===== AUTH SERVICE ROUTES =====

// Public auth endpoints (no authentication required)
router.use('/auth/register', authLimiter, authServiceProxy);
router.use('/auth/register/batch', authLimiter, authServiceProxy);
router.use('/auth/login', authLimiter, authServiceProxy);

// Internal service endpoints
router.use('/auth/verify-token', verifyInternalService, authServiceProxy);

// Internal token balance endpoint (for service-to-service communication)
router.put('/auth/token-balance', verifyInternalService, authServiceProxy);

// Protected user endpoints
router.use('/auth/logout', verifyToken, authServiceProxy);
router.use('/auth/change-password', verifyToken, authServiceProxy);

// Profile endpoints (all HTTP methods)
router.get('/auth/profile', verifyToken, authServiceProxy);
router.put('/auth/profile', verifyToken, authServiceProxy);
router.delete('/auth/profile', verifyToken, authServiceProxy);

// User token balance endpoint (for user queries)
router.get('/auth/token-balance', verifyToken, authServiceProxy);

// School endpoints (protected)
router.get('/auth/schools', verifyToken, authServiceProxy);
router.post('/auth/schools', verifyToken, authServiceProxy);

// Specific school endpoints (must come before general /auth/schools/* pattern)
router.get('/auth/schools/by-location', verifyToken, authServiceProxy);
router.get('/auth/schools/location-stats', verifyToken, authServiceProxy);
router.get('/auth/schools/distribution', verifyToken, authServiceProxy);

// School users endpoint (with parameter)
router.get('/auth/schools/:schoolId/users', verifyToken, authServiceProxy);

// ===== ADMIN ROUTES =====

// Admin authentication (public)
router.post('/admin/login', authLimiter, authServiceProxy);

// Protected admin endpoints
router.get('/admin/profile', verifyToken, verifyAdmin, adminLimiter, authServiceProxy);
router.put('/admin/profile', verifyToken, verifyAdmin, adminLimiter, authServiceProxy);
router.post('/admin/change-password', verifyToken, verifyAdmin, adminLimiter, authServiceProxy);
router.post('/admin/logout', verifyToken, verifyAdmin, adminLimiter, authServiceProxy);

// Superadmin only endpoints
router.post('/admin/register', verifyToken, verifyAdmin, adminLimiter, authServiceProxy);

// ===== ARCHIVE SERVICE ROUTES =====

// Analysis Results endpoints
router.use('/archive/results', verifyToken, archiveServiceProxy);

// Analysis Jobs endpoints
router.use('/archive/jobs', verifyToken, archiveServiceProxy);

// Additional archive endpoints
router.use('/archive/jobs/stats', verifyToken, archiveServiceProxy);

// Internal service endpoints for archive
router.use('/archive/batch', verifyInternalService, archiveServiceProxy);

// Development endpoints (if enabled)
if (process.env.NODE_ENV === 'development') {
  router.use('/archive/dev', verifyInternalService, archiveServiceProxy);
}

// ===== ASSESSMENT SERVICE ROUTES =====

// Assessment submission (protected + rate limited)
router.use('/assessment/submit', verifyToken, assessmentLimiter, assessmentServiceProxy);

// Assessment status check (protected)
router.use('/assessment/status', verifyToken, assessmentServiceProxy);

// Queue monitoring (protected)
router.use('/assessment/queue/status', verifyToken, assessmentServiceProxy);

// Idempotency endpoints (protected)
router.use('/assessment/idempotency/health', verifyToken, assessmentServiceProxy);
router.use('/assessment/idempotency/cleanup', verifyToken, assessmentServiceProxy);

// Internal callback endpoints
router.use('/assessment/callback/completed', verifyInternalService, assessmentServiceProxy);
router.use('/assessment/callback/failed', verifyInternalService, assessmentServiceProxy);
router.use('/assessment/callback', verifyInternalService, assessmentServiceProxy);

// Development endpoints (if enabled)
if (process.env.NODE_ENV === 'development') {
  router.use('/assessment/test', assessmentServiceProxy);
}

// ===== HEALTH CHECK ROUTES =====

// Global health endpoints (no authentication required)
router.get('/health', authServiceProxy); // Main health check
router.get('/health/metrics', authServiceProxy); // Metrics endpoint
router.get('/health/ready', authServiceProxy); // Readiness probe
router.get('/health/live', authServiceProxy); // Liveness probe

// ===== CATCH-ALL ROUTES =====

// Health endpoints for individual services
router.use('/auth/health', authServiceProxy);
router.use('/archive/health', archiveServiceProxy);
router.use('/assessment/health', assessmentServiceProxy);

// Fallback for any other routes to respective services
router.use('/auth/*', authServiceProxy);
router.use('/admin/*', authServiceProxy);
router.use('/archive/*', archiveServiceProxy);
router.use('/assessment/*', assessmentServiceProxy);

module.exports = router;
